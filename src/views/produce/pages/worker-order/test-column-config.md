# 表格列管理功能测试指南

## 功能概述
本次更新为 WorkOrder.vue 组件的表格列管理功能进行了三个主要改进：

### 1. 列拖拽排序功能 ✅
- **功能描述**: 在字段配置面板中添加拖拽手柄，用户可通过拖拽重新排序字段
- **测试步骤**:
  1. 点击表格头部的"字段配置"按钮
  2. 在弹出的面板中，找到每个字段左侧的拖拽手柄（六个点图标）
  3. 拖拽任意字段到新位置
  4. 观察表格列的顺序是否实时更新

### 2. 简化固定列交互逻辑 ✅
- **功能描述**: 移除下拉选择框，改为简单的固定/取消固定按钮
- **业务规则**: 
  - 操作列始终固定在右侧
  - 其他数据列固定时自动固定到左侧
- **测试步骤**:
  1. 在字段配置面板中，找到每个字段右侧的图钉按钮
  2. 点击图钉按钮切换固定状态
  3. 观察按钮状态变化（颜色和图标）
  4. 检查表格中列的固定效果

### 3. 更新显隐控制UI ✅
- **功能描述**: 将显隐控制改为图标按钮形式，使用眼睛图标
- **测试步骤**:
  1. 在字段配置面板中，找到每个字段最右侧的眼睛图标
  2. 点击眼睛图标切换显示/隐藏状态
  3. 观察图标状态变化（开眼/闭眼）
  4. 检查表格中列的显示/隐藏效果

## 新增功能特性

### 搜索功能
- 在字段配置面板顶部有搜索框
- 可以根据字段名称快速筛选字段

### 字段统计
- 面板标题显示当前显示字段数/总字段数
- 实时更新统计信息

### 视觉优化
- 固定的字段有特殊的背景色和边框
- 拖拽时有视觉反馈
- 按钮状态有清晰的颜色区分

## 技术实现

### 依赖库
- 使用 `vuedraggable@next` 实现拖拽功能
- 使用 Ant Design Vue 图标库

### 数据结构更新
- TableColumn 接口新增 `order` 字段用于排序
- TableColumn 接口新增 `icon` 字段用于显示图标
- 优化 `fixed` 字段的类型定义

### 组件架构
- 新增 `ColumnConfigPanel.vue` 组件
- 保持与原有代码的兼容性
- 响应式设计，支持不同屏幕尺寸

## 注意事项
1. 拖拽功能需要鼠标操作，触摸设备可能需要额外适配
2. 固定列的数量建议控制在合理范围内，避免影响表格布局
3. 搜索功能区分大小写，可根据需要调整
