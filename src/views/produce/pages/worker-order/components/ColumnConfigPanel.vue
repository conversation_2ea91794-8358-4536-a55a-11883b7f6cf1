<template>
  <div class="column-config-panel">
    <div class="panel-header">
      <div class="panel-title">
        <span class="title-icon">⚙️</span>
        字段配置
        <span class="field-count">{{ visibleCount }}/{{ totalCount }}</span>
      </div>
      <a-input
        v-model:value="searchKeyword"
        placeholder="搜索字段"
        size="small"
        class="search-input"
      >
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
    </div>

    <div class="panel-body">
      <draggable
        v-model="sortedColumns"
        item-key="key"
        handle=".drag-handle"
        @end="handleDragEnd"
        class="column-list"
      >
        <template #item="{ element: column }">
          <div
            v-show="shouldShowColumn(column)"
            class="column-item"
            :class="{ 'column-item--fixed': isColumnFixed(column) }"
          >
            <!-- 拖拽手柄 -->
            <div class="drag-handle">
              <HolderOutlined />
            </div>

            <!-- 字段图标和名称 -->
            <div class="column-info">
              <span class="column-title">{{ column.title }}</span>
            </div>

            <!-- 固定列按钮 -->
            <a-button
              v-if="column.key !== 'action'"
              type="text"
              size="small"
              class="pin-button"
              :class="{ 'pin-button--active': isColumnFixed(column) }"
              @click="toggleColumnFixed(column)"
            >
              <PushpinOutlined v-if="isColumnFixed(column)" />
              <PushpinFilled v-else />
            </a-button>

            <!-- 显隐控制按钮 -->
            <a-button
              type="text"
              size="small"
              class="visibility-button"
              :class="{ 'visibility-button--hidden': !column.visible }"
              @click="toggleColumnVisibility(column)"
            >
              <EyeOutlined v-if="column.visible" />
              <EyeInvisibleOutlined v-else />
            </a-button>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import draggable from 'vuedraggable'
import type { TableColumn } from '../tableColumns'

interface Props {
  columns: TableColumn[]
}

interface Emits {
  (e: 'update:columns', columns: TableColumn[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 搜索关键词
const searchKeyword = ref('')

// 排序后的列数据
const sortedColumns = computed({
  get: () => [...props.columns].sort((a, b) => a.order - b.order),
  set: (newColumns: TableColumn[]) => {
    // 更新排序
    const updatedColumns = newColumns.map((col, index) => ({
      ...col,
      order: index + 1,
    }))
    emit('update:columns', updatedColumns)
  },
})

// 统计信息
const totalCount = computed(() => props.columns.length)
const visibleCount = computed(() => props.columns.filter((col) => col.visible).length)

// 判断是否应该显示列
const shouldShowColumn = (column: TableColumn) => {
  if (!searchKeyword.value) return true
  return column.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
}

// 判断列是否固定
const isColumnFixed = (column: TableColumn) => {
  return column.fixed === 'left' || column.fixed === 'right'
}

// 切换列固定状态
const toggleColumnFixed = (column: TableColumn) => {
  const updatedColumns = props.columns.map((col) => {
    if (col.key === column.key) {
      // 操作列固定在右侧，其他列固定在左侧
      if (col.key === 'action') {
        return { ...col, fixed: col.fixed ? false : ('right' as const) }
      } else {
        return { ...col, fixed: col.fixed ? false : ('left' as const) }
      }
    }
    return col
  })
  emit('update:columns', updatedColumns)
}

// 切换列显隐状态
const toggleColumnVisibility = (column: TableColumn) => {
  const updatedColumns = props.columns.map((col) => {
    if (col.key === column.key) {
      return { ...col, visible: !col.visible }
    }
    return col
  })
  emit('update:columns', updatedColumns)
}

// 拖拽结束处理
const handleDragEnd = () => {
  // 拖拽结束后，sortedColumns 的 setter 会自动触发
}
</script>

<style scoped lang="scss">
.column-config-panel {
  width: 360px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .panel-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-weight: 500;
      font-size: 14px;

      .title-icon {
        font-size: 16px;
      }

      .field-count {
        color: #666;
        font-size: 12px;
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: auto;
      }
    }

    .search-input {
      width: 100%;
    }
  }

  .panel-body {
    max-height: 450px;
    overflow-y: auto;

    .column-list {
      padding: 8px 12px;
    }

    .column-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 10px 8px;
      border-radius: 6px;
      transition: all 0.2s;
      border: 1px solid transparent;
      margin-bottom: 4px;

      &:hover {
        background: #fafafa;
        border-color: #e6f7ff;
      }

      &--fixed {
        background: #e6f7ff;
        border-color: #91d5ff;

        .column-info .column-title {
          color: #1890ff;
          font-weight: 500;
        }
      }

      .drag-handle {
        cursor: grab;
        color: #bfbfbf;
        padding: 2px;
        font-size: 12px;

        &:hover {
          color: #8c8c8c;
        }

        &:active {
          cursor: grabbing;
        }
      }

      .column-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 0;

        .column-icon {
          color: #8c8c8c;
          font-size: 16px;
          flex-shrink: 0;
        }

        .column-title {
          font-size: 14px;
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .pin-button {
        color: #bfbfbf;
        padding: 4px;
        border-radius: 4px;

        &--active {
          color: #52c41a;
          background: #f6ffed;
        }

        &:hover {
          color: #52c41a;
          background: #f6ffed;
        }
      }

      .visibility-button {
        color: #52c41a;
        padding: 4px;
        border-radius: 4px;

        &--hidden {
          color: #bfbfbf;

          &:hover {
            color: #8c8c8c;
            background: #fafafa;
          }
        }

        &:hover {
          background: #f6ffed;
        }
      }
    }
  }
}

// 滚动条样式
.panel-body::-webkit-scrollbar {
  width: 6px;
}

.panel-body::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
</style>
