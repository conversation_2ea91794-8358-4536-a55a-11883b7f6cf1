export interface TableColumn {
  title: string
  dataIndex?: string
  key: string
  width?: number
  visible: boolean
  fixed?: 'left' | 'right'
}

export const tableColumns: TableColumn[] = [
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    visible: true,
    width: 120,
    fixed: 'left',
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    visible: true,
    width: 120,
    fixed: 'left',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    visible: true,
    width: 150,
    fixed: 'left',
  },
  { title: '状态', dataIndex: 'status', key: 'status', visible: true, width: 80 },
  { title: '工单进度条', key: 'progressBar', visible: true, width: 120 },
  {
    title: '计划开始时间',
    dataIndex: 'planStartTime',
    key: 'planStartTime',
    visible: true,
    width: 150,
  },
  {
    title: '计划结束时间',
    dataIndex: 'planEndTime',
    key: 'planEndTime',
    visible: true,
    width: 150,
  },
  { title: '图片/图纸', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 100 },
  { title: '关联单据', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 100 },
  { title: '计划数', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 80 },
  { title: '实际数', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 80 },
  { title: '良品数', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 80 },
  { title: '不良品数', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 100 },
  { title: '备注', dataIndex: 'planEndTime', key: 'planEndTime', visible: true, width: 120 },
  { title: '创建人', dataIndex: 'createUser', key: 'createUser', visible: true, width: 100 },
  { title: '更新人', dataIndex: 'updateUser', key: 'updateUser', visible: true, width: 100 },
  { title: '单位', dataIndex: 'updateUser', key: 'updateUser', visible: true, width: 60 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', visible: true, width: 150 },
  { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 150 },
  { title: '实际开始时间', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 150 },
  { title: '实际结束时间', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 150 },
  { title: '累计工时', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '登录客户', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '产品单重', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '损耗', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 80 },
  { title: '关联', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 80 },
  { title: '当前工序', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '关联销售订单', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 150 },
  { title: '剩余数', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 80 },
  { title: '任务数', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 80 },
  { title: '工序进度', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '工序进度(%)', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 120 },
  { title: '工单进度', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '工单进度(%)', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 120 },
  { title: '时间进度(%)', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 120 },
  { title: '累计工资', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '工期状态', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 100 },
  { title: '计划生产周期', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 130 },
  { title: '实际生产周期', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 130 },
  { title: '生产周期倍率', dataIndex: 'updateTime', key: 'updateTime', visible: true, width: 130 },
  { title: '操作', key: 'action', visible: true, width: 120, fixed: 'right' },
]
