import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/home/<USER>'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '首页',
      },
    },
    {
      path: '/layout-alignment',
      name: 'layoutAlignment',
      component: () => import('../views/demo/LayoutAlignmentDemo.vue'),
      meta: {
        title: '布局对齐演示',
      },
    },
    {
      path: '/advance-search',
      name: 'advanceSearch',
      component: () => import('../views/demo/AdvancedSearchFormDemo.vue'),
      meta: {
        title: '高级搜索演示',
      },
    },
    {
      path: '/produce',
      name: 'Produce',
      component: () => import('../views/produce/index.vue'),
      meta: {
        title: '生产管理',
      },
      children: [
        {
          path: 'work-order',
          name: 'ProduceWorkOrder',
          component: () => import('../views/produce/pages/worker-order/WorkOrder.vue'),
          meta: {
            title: '工单',
          },
        },
        {
          path: 'task',
          name: 'ProduceTask',
          component: () => import('../views/produce/pages/Task.vue'),
          meta: {
            title: '任务',
          },
        },
        {
          path: 'material-list',
          name: 'ProduceMaterialList',
          component: () => import('../views/produce/pages/MaterialList.vue'),
          meta: {
            title: '用料清单',
          },
        },
        {
          path: 'report',
          name: 'ProduceReport',
          component: () => import('../views/produce/pages/Report.vue'),
          meta: {
            title: '报工',
          },
        },
        {
          path: 'report-workbench',
          name: 'ProduceReportWorkbench',
          component: () => import('../views/produce/pages/ReportWorkbench.vue'),
          meta: {
            title: '报工工作台',
          },
        },
      ],
    },
  ],
})

export default router
